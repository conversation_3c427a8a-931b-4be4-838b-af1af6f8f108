import 'package:flutter/material.dart';
import '../themes/app_gradients.dart';

/// Widget nền gradient cho toàn bộ ứng dụng theo thiết kế request11.md
class GradientBackground extends StatelessWidget {
  final Widget child;
  final Gradient? gradient;

  const GradientBackground({
    super.key,
    required this.child,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: gradient ?? AppGradients.backgroundGradient,
      ),
      child: child,
    );
  }
}

/// Widget nền gradient đơn giản cho các container nhỏ
class SimpleGradientBackground extends StatelessWidget {
  final Widget child;
  final List<Color>? colors;
  final AlignmentGeometry begin;
  final AlignmentGeometry end;
  final BorderRadius? borderRadius;

  const SimpleGradientBackground({
    super.key,
    required this.child,
    this.colors,
    this.begin = Alignment.topLeft,
    this.end = Alignment.bottomRight,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: begin,
          end: end,
          colors: colors ?? AppGradients.cardGradient.colors,
        ),
        borderRadius: borderRadius,
      ),
      child: child,
    );
  }
}
