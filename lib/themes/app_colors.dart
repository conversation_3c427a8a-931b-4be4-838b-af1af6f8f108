import 'package:flutter/material.dart';

/// Bảng màu ứng dụng thống nhất cho toàn bộ dự án
class AppColors {
  // === MAIN THEME COLORS ===

  // Card Gradient Colors (Pink to Purple)
  static const Color cardPinkStart = Color(0xFFFF9EC7); // Pink sáng
  static const Color cardPurpleEnd = Color(0xFF9C7FFF); // Tím sáng

  // Background Gradient Colors (Purple to Dark)
  static const Color bgPurpleTop = Color(0xFF7758CE); // Tím sáng trên
  static const Color bgBlueMiddle1 = Color(0xFF172679); // Xanh tím
  static const Color bgBlueMiddle2 = Color(0xFF080E46); // Xanh đậm
  static const Color bgDarkBottom = Color(0xFF00000B); // Đen xanh đậm

  // === TEXT COLORS ===
  static const Color textPrimary = Color(0xFFFFFFFF); // Trắng chính
  static const Color textSecondary = Color(0xFFFFFFFF); // Trắng phụ
  static final Color textSubtle = const Color(
    0xFFFFFFFF,
  ).withValues(alpha: 0.8); // Trắng mờ

  // Text colors cho light theme (compatibility)
  static const Color textPrimaryLight = Color(0xFF2C3E50);
  static const Color textSecondaryLight = Color(0xFF7F8C8D);
  static const Color textSubtleLight = Color(0xFFBDC3C7);

  // === SURFACE COLORS ===

  // Light Theme Surfaces
  static final Color surfaceOverlay = const Color(
    0xFFFFFFFF,
  ).withValues(alpha: 0.22);
  static final Color iconBackground = const Color(
    0xFFFFFFFF,
  ).withValues(alpha: 0.22);

  // Dark Theme Surfaces
  static final Color surfaceOverlayDark = const Color(
    0xFF000000,
  ).withValues(alpha: 0.4);
  static final Color iconBackgroundDark = const Color(
    0xFF000000,
  ).withValues(alpha: 0.4);

  // === SEMANTIC COLORS ===
  static const Color primaryColor = Color(0xFF9C7FFF); // Tím chính
  static const Color secondaryColor = Color(0xFFFF9EC7); // Hồng phụ
  static const Color accentColor = Color(0xFF7758CE); // Tím accent
  static const Color errorColor = Color(0xFFF44336); // Đỏ lỗi
  static const Color successColor = Color(0xFF4CAF50); // Xanh thành công
  static const Color warningColor = Color(0xFFFF9800); // Cam cảnh báo
  static const Color infoColor = Color(0xFF2196F3); // Xanh thông tin

  // === MATERIAL COLORS ===
  static const Color materialBrick = Color(0xFFF44336); // Đỏ gạch
  static const Color materialSand = Color(0xFFFF9800); // Cam cát
  static const Color materialCement = Color(0xFF9E9E9E); // Xám xi măng
  static const Color materialSteel = Color(0xFF2196F3); // Xanh thép
  static const Color materialStone = Color(0xFF795548); // Nâu đá
  static const Color materialTile = Color(0xFFFF5722); // Cam đậm ngói
  static const Color materialGypsum = Color(0xFFFFFFFF); // Trắng thạch cao
  static const Color materialPaint = Color(0xFF9C27B0); // Tím sơn
  static const Color materialDoor = Color(0xFF00BCD4); // Cyan cửa
  static const Color materialLabor = Color(0xFFE91E63); // Hồng nhân công

  // === HELPER METHODS ===

  /// Lấy màu surface overlay dựa trên theme hiện tại
  static Color getSurfaceOverlay(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? surfaceOverlayDark : surfaceOverlay;
  }

  /// Lấy màu background icon dựa trên theme hiện tại
  static Color getIconBackground(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? iconBackgroundDark : iconBackground;
  }

  /// Lấy màu text chính dựa trên theme hiện tại
  static Color getTextPrimary(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? textPrimary : textPrimaryLight;
  }

  /// Lấy màu text phụ dựa trên theme hiện tại
  static Color getTextSecondary(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? textSecondary : textSecondaryLight;
  }

  /// Lấy màu text mờ dựa trên theme hiện tại
  static Color getTextSubtle(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? textSubtle : textSubtleLight;
  }

  /// Lấy màu cho vật liệu dựa trên tên
  static Color getMaterialColor(String materialName) {
    switch (materialName.toLowerCase()) {
      case 'gạch':
        return materialBrick;
      case 'cát xây':
      case 'cát trát':
        return materialSand;
      case 'xi măng':
        return materialCement;
      case 'thép':
        return materialSteel;
      case 'đá':
        return materialStone;
      case 'ngói':
        return materialTile;
      case 'thạch cao':
        return materialGypsum;
      case 'sơn':
        return materialPaint;
      case 'cửa nhôm':
      case 'cửa composite':
        return materialDoor;
      case 'nhân công xây dựng':
      case 'nhân công điện nước':
        return materialLabor;
      default:
        return successColor; // Màu mặc định
    }
  }
}
