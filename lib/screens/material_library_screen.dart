import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/material_provider.dart';
import '../models/material_model.dart' as model;
import '../models/brick.dart';
import '../models/custom_material.dart';
import '../services/csv_service.dart';
import '../utils/number_formatter.dart';
import '../themes/app_colors.dart';
import '../themes/app_gradients.dart';

/// <PERSON>àn h<PERSON> quả<PERSON> lý thư viện vật liệu
class MaterialLibraryScreen extends StatefulWidget {
  const MaterialLibraryScreen({super.key});

  @override
  State<MaterialLibraryScreen> createState() => _MaterialLibraryScreenState();
}

class _MaterialLibraryScreenState extends State<MaterialLibraryScreen> {
  final CsvService _csvService = CsvService();
  String _searchQuery = '';
  model.MaterialType? _filterType;
  RangeValues _priceRange = const RangeValues(0, 2000000);
  double _maxPrice = 2000000;

  @override
  void initState() {
    super.initState();

    // Tìm giá cao nhất để thiết lập range slider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final materialProvider = Provider.of<MaterialProvider>(
        context,
        listen: false,
      );
      if (materialProvider.materials.isNotEmpty) {
        final maxPrice = materialProvider.materials
            .map((m) => m.pricePerUnit)
            .reduce((a, b) => a > b ? a : b);

        setState(() {
          _maxPrice = maxPrice.ceilToDouble();
          _priceRange = RangeValues(0, _maxPrice);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thư viện vật liệu'),
        backgroundColor: ConstructionTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: ConstructionTheme.primaryGradient,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.file_upload),
            tooltip: 'Xuất CSV',
            onPressed: _exportToCSV,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            tooltip: 'Nhập CSV',
            onPressed: _importFromCSV,
          ),
        ],
      ),
      body: Column(
        children: [
          // Thanh tìm kiếm
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  ConstructionTheme.primaryColor.withOpacity(0.1),
                  ConstructionTheme.backgroundColor,
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Tìm kiếm vật liệu',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
                hintStyle: TextStyle(color: ConstructionTheme.textLightColor),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: ConstructionTheme.primaryColor,
                    width: 1,
                  ),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // Bộ lọc
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Lọc theo loại vật liệu:',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: ConstructionTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 12),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(null, 'Tất cả'),
                      _buildFilterChip(model.MaterialType.brick, 'Gạch'),
                      _buildFilterChip(model.MaterialType.sand, 'Cát'),
                      _buildFilterChip(model.MaterialType.cement, 'Xi măng'),
                      _buildFilterChip(model.MaterialType.steel, 'Thép'),
                      _buildFilterChip(model.MaterialType.tile, 'Gạch ốp lát'),
                      _buildFilterChip(model.MaterialType.roofTile, 'Ngói'),
                      _buildFilterChip(model.MaterialType.custom, 'Tùy chỉnh'),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Lọc theo giá
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Khoảng giá:',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: ConstructionTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: ConstructionTheme.cardBlueColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        NumberFormatter.formatCurrency(_priceRange.start),
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: ConstructionTheme.textSecondaryColor,
                        ),
                      ),
                    ),
                    Expanded(
                      child: SliderTheme(
                        data: SliderThemeData(
                          activeTrackColor: ConstructionTheme.primaryColor,
                          inactiveTrackColor: ConstructionTheme.primaryColor
                              .withOpacity(0.2),
                          thumbColor: ConstructionTheme.primaryColor,
                          overlayColor: ConstructionTheme.primaryColor
                              .withOpacity(0.1),
                          valueIndicatorColor: ConstructionTheme.primaryColor,
                          valueIndicatorTextStyle: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        child: RangeSlider(
                          values: _priceRange,
                          min: 0,
                          max: _maxPrice,
                          divisions: 20,
                          labels: RangeLabels(
                            NumberFormatter.formatCurrency(_priceRange.start),
                            NumberFormatter.formatCurrency(_priceRange.end),
                          ),
                          onChanged: (values) {
                            setState(() {
                              _priceRange = values;
                            });
                          },
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: ConstructionTheme.cardGreenColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        NumberFormatter.formatCurrency(_priceRange.end),
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: ConstructionTheme.textSecondaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Danh sách vật liệu
          Expanded(
            child: Consumer<MaterialProvider>(
              builder: (context, materialProvider, child) {
                final materials =
                    materialProvider.materials.where((material) {
                      // Lọc theo tên
                      final matchesQuery =
                          _searchQuery.isEmpty ||
                          material.name.toLowerCase().contains(
                            _searchQuery.toLowerCase(),
                          );

                      // Lọc theo loại
                      final matchesType =
                          _filterType == null || material.type == _filterType;

                      // Lọc theo giá
                      final matchesPrice =
                          material.pricePerUnit >= _priceRange.start &&
                          material.pricePerUnit <= _priceRange.end;

                      return matchesQuery && matchesType && matchesPrice;
                    }).toList();

                if (materials.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: ConstructionTheme.textLightColor,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Không tìm thấy vật liệu phù hợp',
                          style: TextStyle(
                            fontSize: 16,
                            color: ConstructionTheme.textSecondaryColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Hãy thử điều chỉnh bộ lọc hoặc tìm kiếm khác',
                          style: TextStyle(
                            fontSize: 14,
                            color: ConstructionTheme.textLightColor,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: materials.length,
                  itemBuilder: (context, index) {
                    final material = materials[index];
                    return _buildMaterialListItem(material, materialProvider);
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: CustomWidgets.circleButton(
        icon: Icons.add,
        onPressed: () {
          _showAddMaterialDialog(context);
        },
        backgroundColor: ConstructionTheme.primaryColor,
        iconColor: Colors.white,
      ),
    );
  }

  /// Xây dựng chip lọc
  Widget _buildFilterChip(model.MaterialType? type, String label) {
    final isSelected = _filterType == type;

    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(
          label,
          style: TextStyle(
            color:
                isSelected ? Colors.white : ConstructionTheme.textPrimaryColor,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        selectedColor: ConstructionTheme.primaryColor,
        backgroundColor: Colors.grey.shade100,
        checkmarkColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color:
                isSelected
                    ? ConstructionTheme.primaryColor
                    : Colors.grey.shade300,
          ),
        ),
        onSelected: (selected) {
          setState(() {
            _filterType = selected ? type : null;
          });
        },
      ),
    );
  }

  /// Xây dựng item vật liệu
  Widget _buildMaterialListItem(
    model.Material material,
    MaterialProvider provider,
  ) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          backgroundColor: _getMaterialColor(material.type),
          child: Icon(_getMaterialIcon(material.type), color: Colors.white),
        ),
        title: Text(
          material.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          '${material.unit} - ${NumberFormatter.formatCurrency(material.pricePerUnit)} VNĐ',
          style: TextStyle(color: ConstructionTheme.textSecondaryColor),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(Icons.edit, color: ConstructionTheme.primaryColor),
              onPressed: () {
                _showEditMaterialDialog(context, material, provider);
              },
            ),
            if (material.type == model.MaterialType.custom)
              IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () {
                  _showDeleteConfirmDialog(context, material, provider);
                },
              ),
          ],
        ),
      ),
    );
  }

  /// Hiển thị dialog thêm vật liệu mới
  void _showAddMaterialDialog(BuildContext context) {
    final nameController = TextEditingController();
    final priceController = TextEditingController();
    final unitController = TextEditingController();
    final lengthController = TextEditingController(text: '0.2');
    final widthController = TextEditingController(text: '0.1');
    final heightController = TextEditingController(text: '0.05');
    var selectedUnit = model.MeasurementUnit.piece;
    var showDimensions = selectedUnit == model.MeasurementUnit.piece;

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                title: const Text('Thêm vật liệu mới'),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextField(
                        controller: nameController,
                        decoration: const InputDecoration(
                          labelText: 'Tên vật liệu',
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: priceController,
                        decoration: const InputDecoration(
                          labelText: 'Giá',
                          prefixText: 'VNĐ ',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DropdownButtonFormField<model.MeasurementUnit>(
                            decoration: const InputDecoration(
                              labelText: 'Đơn vị đo lường',
                            ),
                            value: selectedUnit,
                            items:
                                model.MeasurementUnit.values.map((unit) {
                                  return DropdownMenuItem<
                                    model.MeasurementUnit
                                  >(
                                    value: unit,
                                    child: Text(_getMeasurementUnitLabel(unit)),
                                  );
                                }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  selectedUnit = value;
                                  showDimensions =
                                      value == model.MeasurementUnit.piece;
                                  if (value != model.MeasurementUnit.custom) {
                                    unitController.text =
                                        _getUnitForMeasurementUnit(value);
                                  } else {
                                    unitController.text = '';
                                  }
                                });
                              }
                            },
                          ),
                          if (selectedUnit == model.MeasurementUnit.custom)
                            Padding(
                              padding: const EdgeInsets.only(top: 16),
                              child: TextField(
                                controller: unitController,
                                decoration: const InputDecoration(
                                  labelText: 'Đơn vị tùy chỉnh',
                                ),
                              ),
                            ),

                          // Hiển thị trường nhập kích thước nếu đơn vị là viên
                          if (showDimensions) ...[
                            const SizedBox(height: 16),
                            const Divider(),
                            const SizedBox(height: 8),
                            const Text(
                              'Kích thước viên (m)',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: lengthController,
                                    decoration: const InputDecoration(
                                      labelText: 'Dài',
                                      hintText: '0.2',
                                    ),
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                          decimal: true,
                                        ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextField(
                                    controller: widthController,
                                    decoration: const InputDecoration(
                                      labelText: 'Rộng',
                                      hintText: '0.1',
                                    ),
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                          decimal: true,
                                        ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextField(
                                    controller: heightController,
                                    decoration: const InputDecoration(
                                      labelText: 'Cao',
                                      hintText: '0.05',
                                    ),
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                          decimal: true,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Lưu ý: Kích thước viên ảnh hưởng đến việc tính toán số lượng cần thiết cho công trình.',
                              style: TextStyle(
                                fontSize: 12,
                                fontStyle: FontStyle.italic,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Hủy'),
                  ),
                  TextButton(
                    onPressed: () {
                      if (nameController.text.isEmpty ||
                          priceController.text.isEmpty ||
                          (selectedUnit == model.MeasurementUnit.custom &&
                              unitController.text.isEmpty)) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Vui lòng nhập đầy đủ thông tin'),
                          ),
                        );
                        return;
                      }

                      final price =
                          double.tryParse(priceController.text) ?? 0.0;

                      // Lấy kích thước viên nếu đơn vị là viên
                      double? pieceLength, pieceWidth, pieceHeight;
                      if (showDimensions) {
                        pieceLength = double.tryParse(lengthController.text);
                        pieceWidth = double.tryParse(widthController.text);
                        pieceHeight = double.tryParse(heightController.text);

                        // Kiểm tra giá trị hợp lệ
                        if (pieceLength == null ||
                            pieceWidth == null ||
                            pieceHeight == null ||
                            pieceLength <= 0 ||
                            pieceWidth <= 0 ||
                            pieceHeight <= 0) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Vui lòng nhập kích thước viên hợp lệ',
                              ),
                            ),
                          );
                          return;
                        }
                      }

                      Provider.of<MaterialProvider>(
                        context,
                        listen: false,
                      ).addCustomMaterial(
                        name: nameController.text,
                        pricePerUnit: price,
                        customUnit: unitController.text,
                        measurementUnit: selectedUnit,
                        pieceLength: pieceLength,
                        pieceWidth: pieceWidth,
                        pieceHeight: pieceHeight,
                      );

                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Đã thêm vật liệu mới')),
                      );
                    },
                    child: const Text('Thêm'),
                  ),
                ],
              );
            },
          ),
    );
  }

  /// Hiển thị dialog chỉnh sửa vật liệu
  void _showEditMaterialDialog(
    BuildContext context,
    model.Material material,
    MaterialProvider provider,
  ) {
    final priceController = TextEditingController(
      text: material.pricePerUnit.toString(),
    );

    // Nếu là vật liệu gạch, thêm các trường nhập liệu cho kích thước
    if (material.name == 'Gạch xây' && material is Brick) {
      final lengthController = TextEditingController(
        text: material.brickLength.toString(),
      );
      final widthController = TextEditingController(
        text: material.brickWidth.toString(),
      );
      final heightController = TextEditingController(
        text: material.brickHeight.toString(),
      );

      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: Text('Chỉnh sửa ${material.name}'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: priceController,
                      decoration: InputDecoration(
                        labelText: 'Giá (VNĐ/${material.unit})',
                        prefixText: 'VNĐ ',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 8),
                    const Text(
                      'Kích thước viên gạch',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: lengthController,
                      decoration: const InputDecoration(
                        labelText: 'Chiều dài (m)',
                        hintText: 'Ví dụ: 0.22',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: widthController,
                      decoration: const InputDecoration(
                        labelText: 'Chiều rộng (m)',
                        hintText: 'Ví dụ: 0.10',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: heightController,
                      decoration: const InputDecoration(
                        labelText: 'Chiều cao (m)',
                        hintText: 'Ví dụ: 0.07',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Lưu ý: Kích thước gạch ảnh hưởng đến việc tính toán số lượng gạch cần thiết cho công trình.',
                      style: TextStyle(
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Hủy'),
                ),
                TextButton(
                  onPressed: () {
                    final price =
                        double.tryParse(priceController.text) ??
                        material.pricePerUnit;
                    provider.updateMaterialPrice(material.name, price);

                    // Cập nhật kích thước gạch
                    final length =
                        double.tryParse(lengthController.text) ??
                        material.brickLength;
                    final width =
                        double.tryParse(widthController.text) ??
                        material.brickWidth;
                    final height =
                        double.tryParse(heightController.text) ??
                        material.brickHeight;

                    provider.updateBrickDimensions(length, width, height);

                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Đã cập nhật thông số gạch'),
                      ),
                    );
                  },
                  child: const Text('Lưu'),
                ),
              ],
            ),
      );
    } else if (material.type == model.MaterialType.custom &&
        material.measurementUnit == model.MeasurementUnit.piece &&
        material is CustomMaterial) {
      // Dialog chỉnh sửa cho vật liệu tùy chỉnh loại viên
      final lengthController = TextEditingController(
        text: material.pieceLength?.toString() ?? '0.2',
      );
      final widthController = TextEditingController(
        text: material.pieceWidth?.toString() ?? '0.1',
      );
      final heightController = TextEditingController(
        text: material.pieceHeight?.toString() ?? '0.05',
      );

      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: Text('Chỉnh sửa ${material.name}'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: priceController,
                      decoration: InputDecoration(
                        labelText: 'Giá (VNĐ/${material.unit})',
                        prefixText: 'VNĐ ',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 8),
                    const Text(
                      'Kích thước viên (m)',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: lengthController,
                            decoration: const InputDecoration(
                              labelText: 'Dài',
                              hintText: '0.2',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: TextField(
                            controller: widthController,
                            decoration: const InputDecoration(
                              labelText: 'Rộng',
                              hintText: '0.1',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: TextField(
                            controller: heightController,
                            decoration: const InputDecoration(
                              labelText: 'Cao',
                              hintText: '0.05',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Lưu ý: Kích thước viên ảnh hưởng đến việc tính toán số lượng cần thiết cho công trình.',
                      style: TextStyle(
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Hủy'),
                ),
                TextButton(
                  onPressed: () {
                    final price =
                        double.tryParse(priceController.text) ??
                        material.pricePerUnit;
                    provider.updateMaterialPrice(material.name, price);

                    // Cập nhật kích thước viên
                    final length = double.tryParse(lengthController.text);
                    final width = double.tryParse(widthController.text);
                    final height = double.tryParse(heightController.text);

                    if (length != null &&
                        width != null &&
                        height != null &&
                        length > 0 &&
                        width > 0 &&
                        height > 0) {
                      provider.updateCustomMaterialDimensions(
                        material.name,
                        length,
                        width,
                        height,
                      );
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Kích thước viên không hợp lệ'),
                        ),
                      );
                      return;
                    }

                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Đã cập nhật vật liệu')),
                    );
                  },
                  child: const Text('Lưu'),
                ),
              ],
            ),
      );
    } else {
      // Dialog chỉnh sửa thông thường cho các vật liệu khác
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: Text('Chỉnh sửa ${material.name}'),
              content: TextField(
                controller: priceController,
                decoration: InputDecoration(
                  labelText: 'Giá (VNĐ/${material.unit})',
                  prefixText: 'VNĐ ',
                ),
                keyboardType: TextInputType.number,
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Hủy'),
                ),
                TextButton(
                  onPressed: () {
                    final price =
                        double.tryParse(priceController.text) ??
                        material.pricePerUnit;
                    provider.updateMaterialPrice(material.name, price);
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Đã cập nhật giá vật liệu')),
                    );
                  },
                  child: const Text('Lưu'),
                ),
              ],
            ),
      );
    }
  }

  /// Hiển thị dialog xác nhận xóa vật liệu
  void _showDeleteConfirmDialog(
    BuildContext context,
    model.Material material,
    MaterialProvider provider,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Xác nhận xóa'),
            content: Text('Bạn có chắc chắn muốn xóa "${material.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Hủy'),
              ),
              TextButton(
                onPressed: () {
                  provider.removeMaterial(material);
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Đã xóa vật liệu')),
                  );
                },
                child: const Text('Xóa'),
              ),
            ],
          ),
    );
  }

  /// Xuất danh sách vật liệu ra file CSV
  Future<void> _exportToCSV() async {
    final materials =
        Provider.of<MaterialProvider>(context, listen: false).materials;

    final filePath = await _csvService.exportMaterialsToCSV(materials);

    if (filePath != null && context.mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Đã xuất file CSV: $filePath')));
    } else if (context.mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Lỗi khi xuất file CSV')));
    }
  }

  /// Nhập danh sách vật liệu từ file CSV
  Future<void> _importFromCSV() async {
    final materials = await _csvService.importMaterialsFromCSV();

    if (materials != null && materials.isNotEmpty && context.mounted) {
      final provider = Provider.of<MaterialProvider>(context, listen: false);

      for (final material in materials) {
        provider.addCustomMaterial(
          name: material['name'],
          pricePerUnit: material['pricePerUnit'],
          customUnit: material['unit'],
          measurementUnit: model.MeasurementUnit.custom,
        );
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Đã nhập ${materials.length} vật liệu')),
      );
    } else if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Không có vật liệu nào được nhập')),
      );
    }
  }

  /// Lấy màu cho loại vật liệu
  Color _getMaterialColor(model.MaterialType type) {
    switch (type) {
      case model.MaterialType.brick:
        return ConstructionTheme.secondaryColor;
      case model.MaterialType.sand:
        return ConstructionTheme.cardYellowColor;
      case model.MaterialType.cement:
        return Colors.grey.shade600;
      case model.MaterialType.steel:
        return ConstructionTheme.cardBlueColor;
      case model.MaterialType.stone:
        return Colors.brown;
      case model.MaterialType.tile:
        return ConstructionTheme.cardGreenColor;
      case model.MaterialType.roofTile:
        return Colors.deepOrange;
      case model.MaterialType.metalSheet:
        return ConstructionTheme.primaryColor;
      case model.MaterialType.insulatedMetal:
        return ConstructionTheme.accentColor;
      case model.MaterialType.gypsum:
        return Colors.grey.shade400;
      case model.MaterialType.exteriorPaint:
        return Colors.green;
      case model.MaterialType.interiorPaint:
        return Colors.lightGreen;
      case model.MaterialType.aluminumDoor:
        return Colors.indigo;
      case model.MaterialType.compositeDoor:
        return ConstructionTheme.cardPurpleColor;
      case model.MaterialType.labor:
        return Colors.teal;
      case model.MaterialType.plumbingLabor:
        return ConstructionTheme.accentColor;
      case model.MaterialType.plumbingMaterial:
        return ConstructionTheme.primaryColor.withOpacity(0.7);
      case model.MaterialType.concreteSand:
        return ConstructionTheme.cardYellowColor.withOpacity(0.8);
      case model.MaterialType.custom:
        return ConstructionTheme.secondaryColor.withOpacity(0.8);
    }
  }

  /// Lấy icon cho loại vật liệu
  IconData _getMaterialIcon(model.MaterialType type) {
    switch (type) {
      case model.MaterialType.brick:
        return Icons.grid_on;
      case model.MaterialType.sand:
        return Icons.grain;
      case model.MaterialType.cement:
        return Icons.inventory_2;
      case model.MaterialType.steel:
        return Icons.straighten;
      case model.MaterialType.stone:
        return Icons.landscape;
      case model.MaterialType.tile:
        return Icons.grid_4x4;
      case model.MaterialType.roofTile:
        return Icons.roofing;
      case model.MaterialType.metalSheet:
        return Icons.view_agenda;
      case model.MaterialType.insulatedMetal:
        return Icons.layers;
      case model.MaterialType.gypsum:
        return Icons.wallpaper;
      case model.MaterialType.exteriorPaint:
        return Icons.format_paint;
      case model.MaterialType.interiorPaint:
        return Icons.format_color_fill;
      case model.MaterialType.aluminumDoor:
        return Icons.door_front_door;
      case model.MaterialType.compositeDoor:
        return Icons.door_sliding;
      case model.MaterialType.labor:
        return Icons.engineering;
      case model.MaterialType.plumbingLabor:
        return Icons.plumbing;
      case model.MaterialType.plumbingMaterial:
        return Icons.water_drop;
      case model.MaterialType.concreteSand:
        return Icons.blur_circular; // Icon cho cát bê tông
      case model.MaterialType.custom:
        return Icons.category;
    }
  }

  /// Lấy nhãn cho đơn vị đo lường
  String _getMeasurementUnitLabel(model.MeasurementUnit unit) {
    switch (unit) {
      case model.MeasurementUnit.piece:
        return 'Viên';
      case model.MeasurementUnit.cubicMeter:
        return 'Mét khối (m³)';
      case model.MeasurementUnit.squareMeter:
        return 'Mét vuông (m²)';
      case model.MeasurementUnit.meter:
        return 'Mét (m)';
      case model.MeasurementUnit.kilogram:
        return 'Kilogram (kg)';
      case model.MeasurementUnit.ton:
        return 'Tấn';
      case model.MeasurementUnit.set:
        return 'Bộ';
      case model.MeasurementUnit.package:
        return 'Gói';
      case model.MeasurementUnit.custom:
        return 'Tùy chỉnh';
    }
  }

  /// Lấy đơn vị cho đơn vị đo lường
  String _getUnitForMeasurementUnit(model.MeasurementUnit unit) {
    switch (unit) {
      case model.MeasurementUnit.piece:
        return 'viên';
      case model.MeasurementUnit.cubicMeter:
        return 'm³';
      case model.MeasurementUnit.squareMeter:
        return 'm²';
      case model.MeasurementUnit.meter:
        return 'm';
      case model.MeasurementUnit.kilogram:
        return 'kg';
      case model.MeasurementUnit.ton:
        return 'tấn';
      case model.MeasurementUnit.set:
        return 'bộ';
      case model.MeasurementUnit.package:
        return 'gói';
      case model.MeasurementUnit.custom:
        return '';
    }
  }
}
