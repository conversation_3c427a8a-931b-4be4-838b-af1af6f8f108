import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/project/project_model.dart';
import '../models/project/building_model.dart';
import '../models/project/project_provider.dart';
import '../models/material_provider.dart';
import '../utils/number_formatter.dart';
import '../services/material_calculator.dart';
import '../themes/app_colors.dart';
import '../themes/app_gradients.dart';

/// <PERSON>àn hình chi tiết dự án
class ProjectDetailScreen extends StatefulWidget {
  /// Dự án cần hiển thị
  final Project project;

  const ProjectDetailScreen({super.key, required this.project});

  @override
  State<ProjectDetailScreen> createState() => _ProjectDetailScreenState();
}

class _ProjectDetailScreenState extends State<ProjectDetailScreen> {
  late Project _project;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _project = widget.project;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_project.name),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(gradient: AppGradients.buttonGradient),
        ),
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.check : Icons.edit),
            onPressed: () {
              setState(() {
                _isEditing = !_isEditing;

                if (!_isEditing) {
                  // Lưu thay đổi
                  Provider.of<ProjectProvider>(
                    context,
                    listen: false,
                  ).updateProject(_project);

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Đã lưu thay đổi'),
                      backgroundColor: AppColors.primaryColor,
                    ),
                  );
                }
              });
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'export') {
                _exportPDF();
              } else if (value == 'share') {
                _shareProject();
              }
            },
            icon: const Icon(Icons.more_vert, color: Colors.white),
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'export',
                    child: Row(
                      children: [
                        Icon(
                          Icons.picture_as_pdf,
                          color: AppColors.primaryColor,
                        ),
                        const SizedBox(width: 8),
                        const Text('Xuất PDF'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(Icons.share, color: AppColors.secondaryColor),
                        const SizedBox(width: 8),
                        const Text('Chia sẻ'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Thông tin cơ bản
            _buildBasicInfo(),

            const SizedBox(height: 24),

            // Thông tin tầng và mái
            _buildFloorsAndRoofInfo(),

            const SizedBox(height: 24),

            // Biểu đồ chi phí
            _buildCostChart(),

            const SizedBox(height: 24),

            // Danh sách vật liệu
            _buildMaterialList(),
          ],
        ),
      ),
    );
  }

  /// Xây dựng phần thông tin cơ bản
  Widget _buildBasicInfo() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Ảnh dự án với gradient overlay
          if (_project.imagePath != null && _project.imagePath!.isNotEmpty)
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  child: Image.file(
                    File(_project.imagePath!),
                    height: 200,
                    width: double.infinity,
                    fit: BoxFit.cover,
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 80,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withAlpha(179),
                        ],
                      ),
                    ),
                  ),
                ),
                Positioned(
                  bottom: 16,
                  left: 16,
                  child: Text(
                    _project.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          offset: Offset(1, 1),
                          blurRadius: 3,
                          color: Colors.black45,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

          // Thông tin chi tiết
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Tên dự án
                Row(
                  children: [
                    Icon(Icons.home_work, color: AppColors.primaryColor),
                    const SizedBox(width: 8),
                    Expanded(
                      child:
                          _isEditing
                              ? TextFormField(
                                initialValue: _project.name,
                                decoration: const InputDecoration(
                                  labelText: 'Tên công trình',
                                ),
                                onChanged: (value) {
                                  _project.name = value;
                                },
                              )
                              : Text(
                                _project.name,
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Địa điểm
                Row(
                  children: [
                    Icon(Icons.location_on, color: AppColors.secondaryColor),
                    const SizedBox(width: 8),
                    Expanded(
                      child:
                          _isEditing
                              ? TextFormField(
                                initialValue: _project.location,
                                decoration: const InputDecoration(
                                  labelText: 'Địa điểm',
                                ),
                                onChanged: (value) {
                                  _project.location = value;
                                },
                              )
                              : Text(
                                _project.location,
                                style: TextStyle(
                                  color: AppColors.getTextSecondary(context),
                                ),
                              ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Loại móng
                Row(
                  children: [
                    Icon(Icons.foundation, color: AppColors.primaryColor),
                    const SizedBox(width: 8),
                    Expanded(
                      child:
                          _isEditing
                              ? DropdownButtonFormField<FoundationType>(
                                value: _project.foundationType,
                                decoration: const InputDecoration(
                                  labelText: 'Loại móng',
                                ),
                                items:
                                    FoundationType.values.map((type) {
                                      return DropdownMenuItem<FoundationType>(
                                        value: type,
                                        child: Text(
                                          _getFoundationTypeName(type),
                                        ),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    _project.foundationType = value;
                                  }
                                },
                              )
                              : Text(
                                _project.foundationType != null
                                    ? _getFoundationTypeName(
                                      _project.foundationType!,
                                    )
                                    : 'Chưa chọn',
                                style: TextStyle(
                                  color: AppColors.getTextSecondary(context),
                                ),
                              ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Loại kết cấu
                Row(
                  children: [
                    Icon(Icons.architecture, color: AppColors.infoColor),
                    const SizedBox(width: 8),
                    Expanded(
                      child:
                          _isEditing
                              ? DropdownButtonFormField<StructureType>(
                                value: _project.structureType,
                                decoration: const InputDecoration(
                                  labelText: 'Loại kết cấu',
                                ),
                                items:
                                    StructureType.values.map((type) {
                                      return DropdownMenuItem<StructureType>(
                                        value: type,
                                        child: Text(
                                          _getStructureTypeName(type),
                                        ),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    _project.structureType = value;
                                  }
                                },
                              )
                              : Text(
                                _project.structureType != null
                                    ? _getStructureTypeName(
                                      _project.structureType!,
                                    )
                                    : 'Chưa chọn',
                                style: TextStyle(
                                  color: ConstructionTheme.textSecondaryColor,
                                ),
                              ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Xây dựng phần thông tin tầng và mái
  Widget _buildFloorsAndRoofInfo() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin tầng và mái',
              style: Theme.of(context).textTheme.titleLarge,
            ),

            const SizedBox(height: 16),

            // Danh sách tầng
            Text(
              'Các tầng:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: ConstructionTheme.textPrimaryColor,
              ),
            ),

            const SizedBox(height: 8),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _project.floors.length,
              itemBuilder: (context, index) {
                final floor = _project.floors[index];
                return _buildFloorItem(floor);
              },
            ),

            const SizedBox(height: 16),

            // Thông tin mái
            if (_project.roof != null) ...[
              Text(
                'Mái:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: ConstructionTheme.textPrimaryColor,
                ),
              ),

              const SizedBox(height: 8),

              Row(
                children: [
                  Text(
                    'Loại mái:',
                    style: TextStyle(
                      color: ConstructionTheme.textSecondaryColor,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getRoofTypeName(_project.roof!.type),
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ],
              ),

              const SizedBox(height: 4),

              Row(
                children: [
                  Text(
                    'Diện tích mái:',
                    style: TextStyle(
                      color: ConstructionTheme.textSecondaryColor,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${NumberFormatter.format(_project.roof!.area)} m²',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Xây dựng item tầng
  Widget _buildFloorItem(Floor floor) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'Tầng ${floor.number}:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: ConstructionTheme.primaryColor,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'Diện tích: ${NumberFormatter.format(floor.area)} m²',
              style: TextStyle(color: ConstructionTheme.textSecondaryColor),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'Cao: ${NumberFormatter.format(floor.height)} m',
              style: TextStyle(color: ConstructionTheme.textSecondaryColor),
            ),
          ),
        ],
      ),
    );
  }

  /// Xây dựng biểu đồ chi phí
  Widget _buildCostChart() {
    return Consumer<MaterialProvider>(
      builder: (context, materialProvider, child) {
        // Lọc các vật liệu đã chọn
        final selectedMaterials =
            materialProvider.materials
                .where(
                  (material) =>
                      _project.selectedMaterialIds.contains(material.name),
                )
                .toList();

        // Tính tổng chi phí và tạo dữ liệu cho biểu đồ
        double totalCost = 0;
        final Map<String, double> materialCosts = {};

        // Tính chi phí cho từng vật liệu
        for (final material in selectedMaterials) {
          final quantity =
              _project.results['quantities']?[material.name] as double? ?? 0.0;
          final cost = material.pricePerUnit * quantity;
          materialCosts[material.name] = cost;
          totalCost += cost;
        }

        // Nếu không có dữ liệu, hiển thị thông báo
        if (totalCost == 0 || selectedMaterials.isEmpty) {
          return Card(
            elevation: 3,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Phân bổ chi phí',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const Spacer(),
                      if (_isEditing)
                        TextButton.icon(
                          onPressed: _calculateCosts,
                          icon: Icon(
                            Icons.refresh,
                            color: ConstructionTheme.primaryColor,
                          ),
                          label: Text(
                            'Tính toán',
                            style: TextStyle(
                              color: ConstructionTheme.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  Center(
                    child: Padding(
                      padding: const EdgeInsets.all(40),
                      child: Text(
                        'Chưa có dữ liệu chi phí. Hãy chọn vật liệu và tính toán chi phí.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: ConstructionTheme.textLightColor,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // Tạo dữ liệu cho biểu đồ
        final List<PieChartSectionData> chartData = [];

        // Màu sắc cho biểu đồ theo theme
        final colors = [
          AppColors.primaryColor,
          AppColors.secondaryColor,
          AppColors.successColor,
          AppColors.infoColor,
          AppColors.primaryColor,
          AppColors.warningColor,
          AppColors.accentColor,
          Colors.teal,
          Colors.amber,
          Colors.indigo,
        ];

        // Thêm các vật liệu có chi phí lớn nhất vào biểu đồ
        final sortedMaterials =
            materialCosts.entries.toList()
              ..sort((a, b) => b.value.compareTo(a.value));

        // Hiển thị tối đa 5 vật liệu, còn lại gộp vào "Khác"
        double otherCost = 0;
        for (int i = 0; i < sortedMaterials.length; i++) {
          if (i < 5) {
            final entry = sortedMaterials[i];
            final percentage = (entry.value / totalCost) * 100;

            // Chỉ hiển thị vật liệu có chi phí > 0
            if (entry.value > 0) {
              chartData.add(
                PieChartSectionData(
                  value: entry.value,
                  title: '${percentage.toStringAsFixed(1)}%',
                  titleStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  color: colors[i % colors.length],
                  radius: 80,
                ),
              );
            }
          } else {
            otherCost += sortedMaterials[i].value;
          }
        }

        // Thêm phần "Khác" nếu có
        if (otherCost > 0) {
          final percentage = (otherCost / totalCost) * 100;
          chartData.add(
            PieChartSectionData(
              value: otherCost,
              title: '${percentage.toStringAsFixed(1)}%',
              titleStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              color: Colors.grey,
              radius: 80,
            ),
          );
        }

        return Card(
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Phân bổ chi phí',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const Spacer(),
                    if (_isEditing)
                      TextButton.icon(
                        onPressed: _calculateCosts,
                        icon: Icon(
                          Icons.refresh,
                          color: ConstructionTheme.primaryColor,
                        ),
                        label: Text(
                          'Tính lại',
                          style: TextStyle(
                            color: ConstructionTheme.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 16),

                // Biểu đồ
                SizedBox(
                  height: 300,
                  child: PieChart(
                    PieChartData(
                      sections: chartData,
                      centerSpaceRadius: 40,
                      sectionsSpace: 2,
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Chú thích
                Wrap(
                  spacing: 16,
                  runSpacing: 8,
                  children: [
                    for (int i = 0; i < sortedMaterials.length && i < 5; i++)
                      if (sortedMaterials[i].value > 0)
                        _buildLegendItem(
                          sortedMaterials[i].key,
                          colors[i % colors.length],
                        ),
                    if (otherCost > 0) _buildLegendItem('Khác', Colors.grey),
                  ],
                ),

                const SizedBox(height: 16),

                // Tổng chi phí
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 16,
                  ),
                  decoration: BoxDecoration(
                    color: ConstructionTheme.cardGreenColor.withAlpha(77),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Tổng chi phí: ',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: ConstructionTheme.textPrimaryColor,
                        ),
                      ),
                      Text(
                        '${NumberFormatter.formatCurrency(totalCost)} VNĐ',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: ConstructionTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Xây dựng item chú thích
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 4),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  /// Thêm phương thức tính toán chi phí
  void _calculateCosts() {
    // Tạo đối tượng DetailedParameters từ dữ liệu dự án
    final detailedParameters = _project.detailedParameters;

    // Kiểm tra xem có thông số chi tiết hay không
    if (detailedParameters.foundation.isEmpty &&
        detailedParameters.walls.isEmpty &&
        detailedParameters.doors.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Không có thông số chi tiết để tính toán'),
        ),
      );
      return;
    }

    // Tính toán vật liệu dựa trên thông số chi tiết
    final Map<String, dynamic> paramsMap = {
      'foundation': detailedParameters.foundation,
      'walls': detailedParameters.walls,
      'doors': detailedParameters.doors,
      'others': detailedParameters.others,
    };

    // Lấy kích thước gạch từ MaterialProvider
    final materialProvider = Provider.of<MaterialProvider>(
      context,
      listen: false,
    );
    final brickDimensions = materialProvider.getBrickDimensions();

    // Tính toán vật liệu với danh sách vật liệu đã chọn, kích thước gạch và thông tin tầng
    final results = MaterialCalculator.calculateMaterialsFromDetailedParams(
      paramsMap,
      selectedMaterialIds: _project.selectedMaterialIds,
      brickDimensions: brickDimensions,
      floors: _project.floors.map((floor) => floor.toMap()).toList(),
    );

    // Cập nhật dự án
    setState(() {
      _project.results = results;
    });

    // Lưu dự án
    Provider.of<ProjectProvider>(
      context,
      listen: false,
    ).updateProject(_project);

    // Hiển thị thông báo
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Đã tính toán chi phí thành công')),
    );
  }

  /// Xây dựng danh sách vật liệu
  Widget _buildMaterialList() {
    return Consumer<MaterialProvider>(
      builder: (context, materialProvider, child) {
        // Lọc các vật liệu đã chọn
        final selectedMaterials =
            materialProvider.materials
                .where(
                  (material) =>
                      _project.selectedMaterialIds.contains(material.name),
                )
                .toList();

        if (selectedMaterials.isEmpty) {
          return Card(
            elevation: 3,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'Chưa có vật liệu nào được chọn',
                  style: TextStyle(
                    color: ConstructionTheme.textLightColor,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ),
          );
        }

        return Card(
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Danh sách vật liệu',
                  style: Theme.of(context).textTheme.titleLarge,
                ),

                const SizedBox(height: 16),

                // Tiêu đề bảng
                Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 12,
                  ),
                  decoration: BoxDecoration(
                    color: ConstructionTheme.primaryColor.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Vật liệu',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: ConstructionTheme.primaryColor,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Đơn giá',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: ConstructionTheme.primaryColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Số lượng',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: ConstructionTheme.primaryColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Thành tiền',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: ConstructionTheme.primaryColor,
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                ),

                const Divider(),

                // Danh sách vật liệu
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: selectedMaterials.length,
                  itemBuilder: (context, index) {
                    final material = selectedMaterials[index];

                    // Lấy số lượng từ kết quả tính toán hoặc mặc định là 0
                    final quantity =
                        _project.results['quantities']?[material.name]
                            as double? ??
                        0.0;
                    final totalPrice = material.pricePerUnit * quantity;

                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        children: [
                          Expanded(flex: 3, child: Text(material.name)),
                          Expanded(
                            flex: 2,
                            child: Text(
                              NumberFormatter.formatCurrency(
                                material.pricePerUnit,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child:
                                _isEditing
                                    ? TextFormField(
                                      initialValue: quantity.toString(),
                                      keyboardType: TextInputType.number,
                                      textAlign: TextAlign.center,
                                      decoration: InputDecoration(
                                        suffixText: material.unit,
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                      ),
                                    )
                                    : Text(
                                      '${quantity.toStringAsFixed(2)} ${material.unit}',
                                      textAlign: TextAlign.center,
                                    ),
                          ),
                          Expanded(
                            flex: 3,
                            child: Text(
                              '${NumberFormatter.formatCurrency(totalPrice)} VNĐ',
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),

                const Divider(),

                // Tính tổng cộng
                Builder(
                  builder: (context) {
                    // Tính tổng chi phí từ tất cả các vật liệu
                    double totalCost = 0;
                    for (final material in selectedMaterials) {
                      final quantity =
                          _project.results['quantities']?[material.name]
                              as double? ??
                          0.0;
                      totalCost += material.pricePerUnit * quantity;
                    }

                    return Container(
                      margin: const EdgeInsets.only(top: 8),
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 16,
                      ),
                      decoration: BoxDecoration(
                        color: ConstructionTheme.cardGreenColor.withAlpha(77),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          const Text(
                            'Tổng cộng:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${NumberFormatter.formatCurrency(totalCost)} VNĐ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: ConstructionTheme.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Lấy tên loại móng
  String _getFoundationTypeName(FoundationType type) {
    switch (type) {
      case FoundationType.strip:
        return 'Móng băng';
      case FoundationType.isolated:
        return 'Móng đơn';
      case FoundationType.pile:
        return 'Móng cọc';
      case FoundationType.raft:
        return 'Móng bè';
    }
  }

  /// Lấy tên loại kết cấu
  String _getStructureTypeName(StructureType type) {
    switch (type) {
      case StructureType.concrete:
        return 'Bê tông cốt thép';
      case StructureType.steel:
        return 'Khung thép';
      case StructureType.brick:
        return 'Gạch';
      case StructureType.wood:
        return 'Gỗ';
    }
  }

  /// Lấy tên loại mái
  String _getRoofTypeName(RoofType type) {
    switch (type) {
      case RoofType.flat:
        return 'Mái bằng';
      case RoofType.metal:
        return 'Mái tôn';
      case RoofType.tile:
        return 'Mái ngói';
    }
  }

  /// Xuất PDF
  void _exportPDF() {
    // TODO: Implement PDF export
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Tính năng đang được phát triển')),
    );
  }

  /// Chia sẻ dự án
  void _shareProject() {
    // TODO: Implement sharing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Tính năng đang được phát triển')),
    );
  }
}
